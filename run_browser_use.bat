@echo off
echo Starting Browser Use...
cd /d "c:\Users\<USER>\Desktop\browser-use-project"
call browser-use-env\Scripts\activate
echo.
echo Browser Use is ready! Choose what to run:
echo 1. Simple Demo (Google search)
echo 2. Examples Menu
echo 3. Custom Task
echo.
set /p choice="Enter your choice (1-3): "

if "%choice%"=="1" (
    echo Running simple demo...
    python simple_demo.py
) else if "%choice%"=="2" (
    echo Running examples menu...
    python examples.py
) else if "%choice%"=="3" (
    echo Enter your custom task:
    set /p task="Task: "
    echo Creating custom task...
    python -c "import asyncio; from browser_use import Agent; from langchain_ollama import ChatOllama; asyncio.run(Agent(task='%task%', llm=ChatOllama(model='mistral:7b', base_url='http://localhost:11434'), use_memory=False).run())"
) else (
    echo Invalid choice. Running simple demo...
    python simple_demo.py
)

echo.
echo Task completed! Press any key to exit...
pause
