import asyncio
from dotenv import load_dotenv
load_dotenv()

from browser_use import Agent
from langchain_ollama import ChatOllama

async def main():
    # Use Ollama with one of your installed models
    llm = ChatOllama(
        model="mistral:7b",  # Using your installed Mistral model
        base_url="http://localhost:11434"
    )
    
    agent = Agent(
        task="Go to Google and search for 'Browser Use Python library'",
        llm=llm,
    )
    
    print("Starting Browser Use agent with Ollama...")
    await agent.run()

if __name__ == "__main__":
    asyncio.run(main())
