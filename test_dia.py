from dia.model import Dia

# Initialize the model
print("Initializing Dia model...")
model = Dia.from_pretrained("nari-labs/Dia-1.6B", compute_dtype="float16")

# Simple test text
text = "[S1] Dia is an open weights text to dialogue model. [S2] You get full control over scripts and voices."

print("Generating audio...")
output = model.generate(text, verbose=True)

print("Saving audio...")
model.save_audio("test_output.mp3", output)

print("Done! Audio saved to test_output.mp3")
