import asyncio
from dotenv import load_dotenv
load_dotenv()

from browser_use import Agent
from langchain_ollama import ChatOllama

async def main():
    # Create LLM instance using your local Ollama
    llm = ChatOllama(
        model="mistral:7b",  # Using your installed Mistral model
        base_url="http://localhost:11434"
    )
    
    # Create Browser Use agent
    agent = Agent(
        task="Go to Google and search for 'what is artificial intelligence'",
        llm=llm,
        use_memory=False  # Disable memory for simplicity
    )
    
    print("🤖 Starting Browser Use demo...")
    print("Task: Go to Google and search for 'what is artificial intelligence'")
    print("=" * 60)
    
    # Run the agent
    await agent.run()
    
    print("=" * 60)
    print("✅ Demo completed!")

if __name__ == "__main__":
    asyncio.run(main())
