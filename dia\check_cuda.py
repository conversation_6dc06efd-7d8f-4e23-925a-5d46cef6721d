import torch
import sys

print(f"Python version: {sys.version}")
print(f"PyTorch version: {torch.__version__}")
print(f"CUDA available: {torch.cuda.is_available()}")
if torch.cuda.is_available():
    print(f"CUDA device count: {torch.cuda.device_count()}")
    print(f"CUDA device name: {torch.cuda.get_device_name(0)}")
    print(f"CUDA version: {torch.version.cuda}")
else:
    print("CUDA is not available. Checking for potential issues...")
    
    # Check if NVIDIA drivers are installed
    try:
        import subprocess
        result = subprocess.run(['nvidia-smi'], capture_output=True, text=True)
        print("nvidia-smi output:")
        print(result.stdout)
    except Exception as e:
        print(f"Error running nvidia-smi: {e}")
        
    # Check PyTorch build info
    print(f"PyTorch debug build: {torch._C._GLIBCXX_USE_CXX11_ABI}")
    print(f"PyTorch build info: {torch.__config__.show()}")
