import asyncio
from dotenv import load_dotenv
load_dotenv()

from browser_use import Agent
from langchain_openai import ChatOpenAI

async def main():
    # Make sure to add your OPENAI_API_KEY to the .env file
    agent = Agent(
        task="Compare the price of gpt-4o and DeepSeek-V3",
        llm=ChatOpenAI(model="gpt-4o"),
    )
    
    print("Starting Browser Use agent with OpenAI...")
    await agent.run()

if __name__ == "__main__":
    asyncio.run(main())
