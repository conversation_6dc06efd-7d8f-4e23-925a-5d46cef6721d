import asyncio
from dotenv import load_dotenv
load_dotenv()

from browser_use import Agent
from langchain_ollama import ChatOllama

# Example 1: Simple Google Search
async def google_search_example():
    llm = ChatOllama(model="mistral:7b", base_url="http://localhost:11434")
    
    agent = Agent(
        task="Go to Google and search for 'Python web scraping tutorials'",
        llm=llm,
        use_memory=False
    )
    
    print("🔍 Starting Google search example...")
    await agent.run()

# Example 2: Price Comparison
async def price_comparison_example():
    llm = ChatOllama(model="mistral:7b", base_url="http://localhost:11434")
    
    agent = Agent(
        task="Compare the price of iPhone 15 on Amazon and Best Buy. Tell me which one is cheaper.",
        llm=llm,
        use_memory=False
    )
    
    print("💰 Starting price comparison example...")
    await agent.run()

# Example 3: News Research
async def news_research_example():
    llm = ChatOllama(model="mistral:7b", base_url="http://localhost:11434")
    
    agent = Agent(
        task="Go to BBC News and find the top 3 technology news stories today. Summarize each one.",
        llm=llm,
        use_memory=False
    )
    
    print("📰 Starting news research example...")
    await agent.run()

# Run examples
async def main():
    print("Browser Use Examples")
    print("===================")
    print("Choose an example to run:")
    print("1. Google Search")
    print("2. Price Comparison") 
    print("3. News Research")
    
    choice = input("Enter your choice (1-3): ")
    
    if choice == "1":
        await google_search_example()
    elif choice == "2":
        await price_comparison_example()
    elif choice == "3":
        await news_research_example()
    else:
        print("Invalid choice. Running Google search example...")
        await google_search_example()

if __name__ == "__main__":
    asyncio.run(main())
