import asyncio
import os
from dotenv import load_dotenv
load_dotenv()

from browser_use import Agent
from langchain_ollama import ChatOllama

class BrowserUseLauncher:
    def __init__(self):
        self.llm = ChatOllama(model="mistral:7b", base_url="http://localhost:11434")
    
    async def run_task(self, task, description=""):
        print(f"\n🤖 {description}")
        print("=" * 60)
        print(f"Task: {task}")
        print("=" * 60)
        
        agent = Agent(
            task=task,
            llm=self.llm,
            use_memory=False
        )
        
        try:
            await agent.run()
            print("\n✅ Task completed successfully!")
        except Exception as e:
            print(f"\n❌ Error: {e}")
        
        input("\nPress Enter to continue...")
    
    def show_menu(self):
        while True:
            os.system('cls' if os.name == 'nt' else 'clear')
            print("🌐 Browser Use Launcher")
            print("=" * 40)
            print("1. 🔍 Google Search")
            print("2. 💰 Price Comparison")
            print("3. 📰 News Research")
            print("4. 🌤️  Weather Check")
            print("5. 🛒 Shopping Research")
            print("6. 📚 Educational Research")
            print("7. ✏️  Custom Task")
            print("8. ❌ Exit")
            print("=" * 40)
            
            choice = input("Choose an option (1-8): ").strip()
            
            if choice == "1":
                query = input("What would you like to search for? ")
                asyncio.run(self.run_task(
                    f"Go to Google and search for '{query}'. Show me the top 3 results.",
                    "Google Search"
                ))
            
            elif choice == "2":
                product = input("What product would you like to compare? ")
                asyncio.run(self.run_task(
                    f"Compare prices for '{product}' on Amazon and eBay. Tell me which is cheaper.",
                    "Price Comparison"
                ))
            
            elif choice == "3":
                topic = input("What news topic are you interested in? ")
                asyncio.run(self.run_task(
                    f"Go to BBC News and find recent articles about '{topic}'. Summarize the top 2 stories.",
                    "News Research"
                ))
            
            elif choice == "4":
                city = input("Which city's weather would you like to check? ")
                asyncio.run(self.run_task(
                    f"Check the weather forecast for {city} on weather.com for the next 3 days.",
                    "Weather Check"
                ))
            
            elif choice == "5":
                item = input("What are you shopping for? ")
                asyncio.run(self.run_task(
                    f"Find the best deals for '{item}' online. Check at least 2 different websites.",
                    "Shopping Research"
                ))
            
            elif choice == "6":
                subject = input("What would you like to learn about? ")
                asyncio.run(self.run_task(
                    f"Find educational resources about '{subject}'. Look for tutorials, courses, or guides.",
                    "Educational Research"
                ))
            
            elif choice == "7":
                custom_task = input("Enter your custom task: ")
                asyncio.run(self.run_task(custom_task, "Custom Task"))
            
            elif choice == "8":
                print("👋 Goodbye!")
                break
            
            else:
                print("❌ Invalid choice. Please try again.")
                input("Press Enter to continue...")

def main():
    print("🚀 Starting Browser Use Launcher...")
    print("Make sure Ollama is running with the mistral:7b model!")
    input("Press Enter to continue...")
    
    launcher = BrowserUseLauncher()
    launcher.show_menu()

if __name__ == "__main__":
    main()
