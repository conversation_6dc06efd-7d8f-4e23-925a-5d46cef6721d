@echo off
title Browser Use Desktop
color 0A

echo.
echo  ██████╗ ██████╗  ██████╗ ██╗    ██╗███████╗███████╗██████╗     ██╗   ██╗███████╗███████╗
echo  ██╔══██╗██╔══██╗██╔═══██╗██║    ██║██╔════╝██╔════╝██╔══██╗    ██║   ██║██╔════╝██╔════╝
echo  ██████╔╝██████╔╝██║   ██║██║ █╗ ██║███████╗█████╗  ██████╔╝    ██║   ██║███████╗█████╗  
echo  ██╔══██╗██╔══██╗██║   ██║██║███╗██║╚════██║██╔══╝  ██╔══██╗    ██║   ██║╚════██║██╔══╝  
echo  ██████╔╝██║  ██║╚██████╔╝╚███╔███╔╝███████║███████╗██║  ██║    ╚██████╔╝███████║███████╗
echo  ╚═════╝ ╚═╝  ╚═╝ ╚═════╝  ╚══╝╚══╝ ╚══════╝╚══════╝╚═╝  ╚═╝     ╚═════╝ ╚══════╝╚══════╝
echo.
echo                              AI-Powered Web Automation
echo.

cd /d "c:\Users\<USER>\Desktop\browser-use-project"
call browser-use-env\Scripts\activate

echo Starting Browser Use Interactive Launcher...
python browser_use_launcher.py

pause
